#!/usr/bin/env python3
"""
Simple Hindi RAG System Launcher
Usage: python3 start.py
"""

import os
import sys
import subprocess

def load_env():
    """Load environment variables from .env file"""
    env_file = '.env'
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                if line.strip() and '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Loaded API key from .env file")

def install_packages():
    """Install required packages"""
    packages = [
        "gradio>=4.0.0",
        "sentence-transformers",
        "faiss-cpu",
        "google-generativeai",
        "PyPDF2",
        "pdfplumber",
        "langdetect",
        "numpy",
        "pandas",
        "python-dotenv"
    ]
    
    print("📦 Installing packages...")
    for package in packages:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    print("✅ All packages installed!")
    return True

def main():
    """Main launcher"""
    print("🇮🇳 Hindi RAG System - Simple Launcher")
    print("=" * 40)
    
    # Load environment variables
    load_env()
    
    # Install packages
    if not install_packages():
        return 1
    
    # Launch the system
    print("🚀 Starting Hindi RAG System...")
    try:
        import clean_ui
        interface = clean_ui.create_interface()
        interface.launch(
            server_port=7864,
            share=False,
            inbrowser=True
        )
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
