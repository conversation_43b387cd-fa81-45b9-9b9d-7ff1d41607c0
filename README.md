# 🇮🇳 Hindi RAG System

A Hindi document Q&A system with ChatGPT-style responses using Gemini API.

## 🚀 Quick Start

### For Mac/Unix (Python 3.13+):
```bash
python3 -m venv venv && source venv/bin/activate && python3 start.py
```

### For other systems:
```bash
python3 start.py
```

## 📚 Features

- **586 document chunks** from 3 Hindi PDFs
- **ChatGPT-style responses** in Hindi
- **5 descriptive questions** with real data
- **Smart fallback** when API quota exceeded
- **Web interface** at http://localhost:7864

## 🔑 API Key

The system uses your existing API key from `.env` file. If you need to update it:

```bash
echo "GEMINI_API_KEY=your-new-key-here" > .env
```

## 📁 Project Structure

```
📁 Hindi_RAG_System/
├── 🚀 start.py              # Simple launcher
├── 🎨 clean_ui.py           # Web interface  
├── 🧠 fast_hindi_rag.py     # Core system
├── ⚡ gemini_llm.py         # Gemini API
├── 📄 pdf_processor.py      # PDF processing
├── 🔍 embedding_system.py   # Embeddings
├── 💾 faiss_vector_store.py # Vector storage
├── ⚙️ config.py             # Configuration
└── 📁 pdfs/                 # PDF documents
```

## 🎯 Sample Questions

1. फ़ाइल नंबर AHS-A/A4/2/2022-XV-1 में कौन सी जानकारी है?
2. राज्य पशुधन मिशन योजना क्या है और इसके मुख्य उद्देश्य क्या हैं?
3. दिशांत सेक्शन ऑफिसर कौन हैं और उनकी क्या जिम्मेदारियां हैं?
4. पशुपालन विभाग के लिए कितना बजट आवंटित है?
5. कंप्यूटर नंबर 35602 का क्या मतलब है?

## 🔧 Troubleshooting

**Port in use**: System will find another port automatically
**API issues**: System has smart fallback responses
**Package errors**: Use virtual environment (see Quick Start)

## 🎉 Ready!

Just run the command and start asking questions in Hindi!
